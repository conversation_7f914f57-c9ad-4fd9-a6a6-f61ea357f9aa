#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试增量更新功能
验证正数加仓和负数减仓的场景
"""

import requests
import json
import time
from datetime import datetime
from position_client_demo import PositionClient

def test_delta_update_api():
    """测试通过API直接发送增量更新"""
    base_url = "http://localhost:5000"
    api_url = f"{base_url}/api/position"
    
    exchange = "binance"
    symbol = "BTCUSDT"
    side = "LONG"
    
    print("🧪 测试增量更新功能 (API方式)")
    print("=" * 60)
    
    # 1. 先开仓
    print("1️⃣ 开始开仓...")
    open_data = {
        "exchange": exchange,
        "symbol": symbol,
        "action": "open_pos",
        "position": {
            "time": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            "open_price": "50000",
            "open_quantity": "100",  # 目标仓位100
            "open_avg_price": "50000",
            "current_quantity": "0",
            "position_side": side
        }
    }
    
    response = requests.post(api_url, json=open_data)
    if response.status_code == 200:
        print("✅ 开仓成功")
    else:
        print(f"❌ 开仓失败: {response.text}")
        return
    
    time.sleep(1)
    
    # 2. 增量加仓测试
    print("\n2️⃣ 增量加仓测试...")
    increments = [50, 30, 20]  # 分别加仓50, 30, 20
    expected_total = 0
    
    for i, delta in enumerate(increments):
        expected_total += delta
        
        update_data = {
            "exchange": exchange,
            "symbol": symbol,
            "action": "update_pos",
            "position": {
                "time": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                "open_price": "50000",
                "open_quantity": "100",
                "open_avg_price": "50000",
                "delta_quantity": str(delta),  # 增量字段
                "position_side": side,
                "current_price": "50100"
            }
        }
        
        response = requests.post(api_url, json=update_data)
        if response.status_code == 200:
            print(f"📈 加仓 +{delta}: 预期总仓位 {expected_total}")
        else:
            print(f"❌ 加仓失败: {response.text}")
        
        time.sleep(1)
    
    # 3. 增量减仓测试
    print("\n3️⃣ 增量减仓测试...")
    decrements = [-20, -30, -40]  # 分别减仓20, 30, 40
    
    for i, delta in enumerate(decrements):
        expected_total += delta
        expected_total = max(0, expected_total)  # 确保不为负数
        
        update_data = {
            "exchange": exchange,
            "symbol": symbol,
            "action": "update_pos",
            "position": {
                "time": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                "open_price": "50000",
                "open_quantity": "100",
                "open_avg_price": "50000",
                "delta_quantity": str(delta),  # 增量字段
                "position_side": side,
                "current_price": "49800"
            }
        }
        
        response = requests.post(api_url, json=update_data)
        if response.status_code == 200:
            print(f"📉 减仓 {delta}: 预期总仓位 {expected_total}")
        else:
            print(f"❌ 减仓失败: {response.text}")
        
        time.sleep(1)
    
    # 4. 获取最终结果
    print("\n4️⃣ 获取最终结果...")
    positions_response = requests.get(f"{base_url}/api/positions")
    if positions_response.status_code == 200:
        positions = positions_response.json()
        final_position = positions.get(exchange, {}).get(symbol, {}).get(side, {})
        final_quantity = float(final_position.get("current_quantity", 0))
        progress = float(final_position.get("progress", 0))
        
        print(f"📊 最终仓位: {final_quantity}")
        print(f"📊 仓位进度: {progress:.1f}%")
        print(f"📊 预期仓位: {expected_total}")
        
        if abs(final_quantity - expected_total) < 0.001:
            print("✅ 增量更新测试通过！")
        else:
            print("❌ 增量更新测试失败！")
    else:
        print(f"❌ 获取仓位失败: {positions_response.text}")

def test_delta_update_client():
    """测试通过客户端进行增量更新"""
    print("\n🧪 测试增量更新功能 (客户端方式)")
    print("=" * 60)
    
    # 创建客户端
    client = PositionClient("http://localhost:5000", "okx")
    
    symbol = "ETHUSDT"
    side = "SHORT"
    
    # 1. 开仓
    print("1️⃣ 开仓...")
    success = client.open_position(
        symbol=symbol,
        position_side=side,
        open_price=3000,
        target_quantity=50,
        current_price=3000
    )
    
    if success:
        print("✅ 开仓成功")
    else:
        print("❌ 开仓失败")
        return
    
    time.sleep(1)
    
    # 2. 增量加仓
    print("\n2️⃣ 增量加仓...")
    deltas = [25, 15, 10]  # 分别加仓25, 15, 10
    
    for delta in deltas:
        success = client.update_position_delta(
            symbol=symbol,
            position_side=side,
            delta_quantity=delta,
            current_price=2980
        )
        
        if success:
            print(f"📈 加仓 +{delta} 成功")
        else:
            print(f"❌ 加仓 +{delta} 失败")
        
        time.sleep(1)
    
    # 3. 增量减仓
    print("\n3️⃣ 增量减仓...")
    deltas = [-10, -20, -30]  # 分别减仓10, 20, 30
    
    for delta in deltas:
        success = client.update_position_delta(
            symbol=symbol,
            position_side=side,
            delta_quantity=delta,
            current_price=3020
        )
        
        if success:
            print(f"📉 减仓 {delta} 成功")
        else:
            print(f"❌ 减仓 {delta} 失败")
        
        time.sleep(1)
    
    # 4. 获取最终结果
    print("\n4️⃣ 获取最终结果...")
    final_data = client.get_position_data(symbol, side)
    if final_data:
        final_quantity = float(final_data.get("current_quantity", 0))
        progress = float(final_data.get("progress", 0))
        
        print(f"📊 最终仓位: {final_quantity}")
        print(f"📊 仓位进度: {progress:.1f}%")
        print("✅ 客户端增量更新测试完成！")
    else:
        print("❌ 获取最终仓位失败")

def main():
    """主函数"""
    print("🚀 开始增量更新功能测试")
    print("请确保服务器已启动 (python app.py)")
    print()
    
    try:
        # 测试API方式
        test_delta_update_api()
        
        time.sleep(2)
        
        # 测试客户端方式
        test_delta_update_client()
        
        print("\n🎉 所有测试完成！")
        
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {e}")

if __name__ == "__main__":
    main()
